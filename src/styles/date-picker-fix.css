/* Fix for date picker positioning issue */
input[type="date"]::-webkit-calendar-picker-indicator {
  position: relative;
  z-index: 1;
  cursor: pointer;
  opacity: 1;
  background: transparent;
  color: #6b7280; /* gray-500 */
  width: 16px;
  height: 16px;
}

/* Ensure date picker dropdown is properly positioned */
input[type="date"] {
  position: relative;
  z-index: auto;
}

/* Fix for webkit date picker */
input[type="date"]::-webkit-datetime-edit {
  position: relative;
}

/* Prevent date picker from moving with scroll - CRITICAL FIX */
input[type="date"]:focus {
  position: relative;
  z-index: 9999 !important;
}

/* Fix for date picker calendar positioning */
input[type="date"]::-webkit-calendar-picker-indicator {
  position: relative !important;
  z-index: 9999 !important;
}

/* Additional fix for date picker calendar */
input[type="date"]::-webkit-calendar-picker-indicator:hover {
  cursor: pointer;
  opacity: 0.8;
}

/* Dark mode support for calendar icon */
.dark input[type="date"]::-webkit-calendar-picker-indicator {
  color: #9ca3af; /* gray-400 */
  filter: invert(1);
}

/* Ensure proper stacking context for date input container */
.date-input-container {
  position: relative;
  z-index: 1;
  isolation: isolate;
}

/* Fix for date picker in modal/overlay contexts */
.date-picker-fixed {
  position: relative !important;
  z-index: 9999 !important;
  isolation: isolate !important;
}

/* Prevent calendar from inheriting transform properties */
input[type="date"]::-webkit-calendar-picker-indicator {
  transform: none !important;
}

/* Ensure calendar icon is visible and properly styled */
input[type="date"]::-webkit-calendar-picker-indicator {
  background-image: none;
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

/* Additional fixes for calendar positioning */
input[type="date"]:focus::-webkit-calendar-picker-indicator {
  position: fixed !important;
  z-index: 10000 !important;
}

/* Ensure calendar dropdown stays in place during scroll */
input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  position: relative;
  z-index: 9999;
}

/* Fix for calendar dropdown positioning */
input[type="date"]::-webkit-inner-spin-button {
  position: relative;
  z-index: 9999;
}

/* Firefox specific fixes */
input[type="date"] {
  appearance: none;
  -moz-appearance: none;
}

/* Ensure calendar stays fixed during scroll for all browsers */
input[type="date"]:focus,
input[type="date"]:active {
  position: relative !important;
  z-index: 10000 !important;
  isolation: isolate !important;
}

/* Additional stacking context fixes */
.date-input-container input[type="date"] {
  position: relative;
  z-index: 1;
}

.date-input-container input[type="date"]:focus {
  z-index: 10000 !important;
}

/* Prevent calendar from being affected by parent transforms */
input[type="date"]::-webkit-calendar-picker-indicator {
  will-change: auto !important;
  backface-visibility: visible !important;
}

/* Ensure calendar dropdown has proper stacking */
input[type="date"]:focus::-webkit-datetime-edit-fields-wrapper {
  z-index: 10001 !important;
}

/* CRITICAL FIX: Override pull-to-refresh transform for date picker */
.ptr__children input[type="date"]:focus {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
}

.ptr__children .date-input-container input[type="date"]:focus {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
}

/* Fix for calendar picker in transformed containers */
.ptr__children input[type="date"]::-webkit-calendar-picker-indicator {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
}

/* Ensure date input creates its own stacking context */
.date-input-container {
  contain: layout style paint !important;
}

/* Override any parent transforms for date picker */
input[type="date"]:focus {
  position: fixed !important;
  z-index: 99999 !important;
  transform: none !important;
  will-change: auto !important;
}

/* Additional override for webkit calendar */
input[type="date"]:focus::-webkit-calendar-picker-indicator {
  position: fixed !important;
  z-index: 100000 !important;
  transform: none !important;
}
